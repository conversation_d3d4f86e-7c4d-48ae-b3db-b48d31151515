'use client';

import React, { useEffect } from 'react';
import { Container, Row, Col, Navbar, Nav, Dropdown } from 'react-bootstrap';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';

// 用户信息组件
const UserInfo: React.FC = () => {
  const { data: session } = useSession();
  if (
    process.env.NEXT_PUBLIC_SSO_ENABLED !== 'true' ||
    !session?.user
  ) return null;
  return (
    <Dropdown align="end">
      <Dropdown.Toggle variant="outline-secondary" id="user-dropdown" size="sm">
        {session.user.name || session.user.email || '用户'}
      </Dropdown.Toggle>
      <Dropdown.Menu>
        <Dropdown.Item onClick={() => signOut({ callbackUrl: '/' })}>
          登出
        </Dropdown.Item>
      </Dropdown.Menu>
    </Dropdown>
  );
};

// 侧边栏菜单项
const menuItems = [
  { path: '/', label: '首页' },
  { path: '/indices', label: '指数行情' },
];

export default function LayoutShell({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // 动态导入 i18n 配置，避免服务器端渲染时的 Context 错误
  useEffect(() => {
    import('../components/i18n');
  }, []);

  return (
    <Container fluid className="root-container p-0">
      {/* 顶部导航栏 */}
      <Navbar bg="light" variant="light" expand="lg" className="topbar">
        <Navbar.Brand className="navbar-brand">
          <span className="navbar-icon" role="img" aria-label="股票数据分析">📊</span>
          {'FI'}
        </Navbar.Brand>
        <Nav className="ms-auto">
          <UserInfo />
        </Nav>
      </Navbar>
      {/* 主体区域 */}
      <Row className="mainrow g-0">
        {/* 侧边栏 */}
        <Col xs={2} md={2} className="sidebar bg-light border-end" style={{ minWidth: 200, maxWidth: 240 }}>
          <Nav className="flex-column py-4">
            {menuItems.map(item => (
              <Nav.Link
                as={Link}
                href={item.path}
                key={item.path}
                className={`sidebar-item mb-2${pathname === item.path ? ' active' : ''}`}
                style={{ fontWeight: 600 }}
              >
                {item.label}
              </Nav.Link>
            ))}
          </Nav>
        </Col>
        {/* 主内容区 */}
        <Col xs={10} md={10} className="maincontent">
          {children}
        </Col>
      </Row>
    </Container>
  );
}