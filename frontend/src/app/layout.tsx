// 国际化初始化
import { Inter } from 'next/font/google';
import 'bootstrap/dist/css/bootstrap.min.css';
import '../styles/globals.css';
import '../styles/app.css';
import { SessionProvider } from 'next-auth/react';
import React from 'react';
import LayoutShell from './LayoutShell';

const inter = Inter({ subsets: ['latin'] });


// 侧边栏菜单项 - 股票数据分析系统
const menuItems = [
  { path: '/', label: '首页' },
  { path: '/indices', label: '指数行情' },
];


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <SessionProvider>
            <LayoutShell>
              {children}
            </LayoutShell>
        </SessionProvider>
      </body>
    </html>
  );
}