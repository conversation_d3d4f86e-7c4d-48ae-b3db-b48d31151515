// 简单的翻译工具，避免使用React Context
const translations = {
  indices_title: "A股主要宽基指数",
  no_indices: "暂无指数数据",
  loading: "加载中...",
  loading_data: "数据加载中...",
  error: "数据加载失败，请稍后重试",
  no_data: "暂无数据",
  range_3m: "最近3个月",
  range_6m: "6个月",
  range_1y: "最近1年",
  name: "名称",
  code: "代码",
  latest_close: "最新收盘",
  change: "涨跌",
  change_percent: "涨跌幅",
  min: "最小值",
  max: "最大值",
};

export function t(key: keyof typeof translations): string {
  return translations[key] || key;
}

// 模拟 i18n 对象，用于语言切换按钮
export const mockI18n = {
  language: "zh",
  changeLanguage: (lang: string) => {
    // 暂时不做任何操作，保持界面一致性
    console.log(`Language changed to: ${lang}`);
  }
};
