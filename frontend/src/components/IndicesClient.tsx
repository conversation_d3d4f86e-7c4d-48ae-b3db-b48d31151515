"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import type { IndexItem } from "../types";
import IndicesTable from "./IndicesTable";
import RangeSelector from "./RangeSelector";
import LineChart from "./LineChart";
import StatusHint from "./StatusHint";
import useQuoteData from "./useQuoteData";



type Range = "3m" | "6m" | "1y";

export default function IndicesClient({
  indices,
}: {
  indices: IndexItem[];
}) {
  const { data: session, status } = useSession();
  const router = useRouter();


  // 当前选中指数
  const [activeSymbol, setActiveSymbol] = useState<string | null>(
    Array.isArray(indices) && indices.length > 0 ? indices[0].symbol : null
  );
  // 选中区间
  const [selectedRange, setSelectedRange] = useState<Range>("3m");

  // 登录校验
  useEffect(() => {
    if (process.env.NODE_ENV === "development") return;
    if (status === "loading") return;
    if (!session) {
      router.replace("/auth/signin");
    }
  }, [session, status, router]);

  // 行情数据请求
  const { data: quoteList, loading: isQuoteLoading, error: isQuoteError } = useQuoteData(
    activeSymbol,
    selectedRange
  );

  // indices 为空兜底处理
  if (!Array.isArray(indices) || indices.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <StatusHint empty emptyText="暂无指数数据" />
      </div>
    );
  }

  // 页面加载状态
  if (status === "loading") {
    return <StatusHint loading loadingText="加载中..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 页面标题 */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            A股主要宽基指数
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            实时监控主要股票指数走势，点击指数查看详细图表
          </p>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* 左侧指数列表 */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  指数列表
                </h2>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  共 {indices.length} 个指数
                </p>
              </div>
              <div className="max-h-[600px] overflow-y-auto">
                <IndicesTable
                  indices={indices}
                  activeSymbol={activeSymbol}
                  onSelect={setActiveSymbol}
                />
              </div>
            </div>
          </div>

          {/* 右侧图表区域 */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">

              {/* 图表头部 */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {activeSymbol ? `${activeSymbol} 走势图` : '请选择指数'}
                    </h2>
                    {activeSymbol && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {indices.find(idx => idx.symbol === activeSymbol)?.name || activeSymbol}
                      </p>
                    )}
                  </div>
                  <RangeSelector
                    selected={selectedRange}
                    loading={isQuoteLoading}
                    onChange={setSelectedRange}
                  />
                </div>
              </div>

              {/* 图表内容 */}
              <div className="p-6">
                <div className="h-[500px] flex items-center justify-center rounded-lg bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
                  {!activeSymbol ? (
                    <div className="text-center">
                      <div className="text-gray-400 dark:text-gray-500 mb-2">
                        <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                      <p className="text-gray-500 dark:text-gray-400">
                        请从左侧列表选择一个指数查看走势图
                      </p>
                    </div>
                  ) : isQuoteLoading ? (
                    <div className="w-full flex flex-col items-center gap-4 animate-pulse">
                      <div className="w-3/4 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      <div className="w-full h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      <div className="flex gap-2">
                        <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                    </div>
                  ) : isQuoteError ? (
                    <StatusHint
                      error={true}
                      errorText="数据加载失败，请稍后重试"
                    />
                  ) : quoteList.length === 0 ? (
                    <StatusHint
                      empty={true}
                      emptyText="暂无数据"
                    />
                  ) : (
                    <div className="w-full h-full">
                      <LineChart data={quoteList} />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}