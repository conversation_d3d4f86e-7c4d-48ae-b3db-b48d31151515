"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import type { IndexItem, QuoteItem } from "../types";
import IndicesTable from "./IndicesTable";
import RangeSelector from "./RangeSelector";
import LineChart from "./LineChart";
import StatusHint from "./StatusHint";
import useQuoteData from "./useQuoteData";

import { useTranslation } from "react-i18next";

type Range = "3m" | "6m" | "1y";

export default function IndicesClient({
  indices,
}: {
  indices: IndexItem[];
}) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isPageLoading, setIsPageLoading] = useState(false);
  const { t, i18n } = useTranslation();

  // 当前选中指数
  const [activeSymbol, setActiveSymbol] = useState<string | null>(
    Array.isArray(indices) && indices.length > 0 ? indices[0].symbol : null
  );
  // 选中区间
  const [selectedRange, setSelectedRange] = useState<Range>("3m");

  // 登录校验
  useEffect(() => {
    if (process.env.NODE_ENV === "development") return;
    if (status === "loading") return;
    if (!session) {
      router.replace("/auth/signin");
    }
  }, [session, status, router]);

  // 行情数据请求
  const { data: quoteList, loading: isQuoteLoading, error: isQuoteError } = useQuoteData(
    activeSymbol,
    selectedRange
  );

  // indices 为空兜底处理
  if (!Array.isArray(indices) || indices.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <StatusHint empty emptyText={t("no_indices")} />
      </div>
    );
  }

  // 页面加载状态
  if (status === "loading" || isPageLoading) {
    return <StatusHint loading loadingText={t("loading")} />;
  }

  return (
    <div
      className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4 min-h-[400px]"
    >
      {/* 语言切换按钮 */}
      <div className="absolute right-6 top-6 z-10 flex gap-2">
        <button
          className={`px-2 py-1 rounded border text-xs font-medium transition-all duration-150 ${
            i18n.language === "zh"
              ? "bg-blue-600 text-white"
              : "bg-white text-blue-600 border-blue-600"
          }`}
          onClick={() => i18n.changeLanguage("zh")}
          aria-label="切换到中文"
        >
          中文
        </button>
        <button
          className={`px-2 py-1 rounded border text-xs font-medium transition-all duration-150 ${
            i18n.language === "en"
              ? "bg-blue-600 text-white"
              : "bg-white text-blue-600 border-blue-600"
          }`}
          onClick={() => i18n.changeLanguage("en")}
          aria-label="Switch to English"
        >
          EN
        </button>
      </div>
      {/* 左侧指数列表 */}
      <div className="w-full md:w-auto max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-4 text-center">{t("indices_title")}</h1>
        <div className="overflow-x-auto">
          <IndicesTable
            indices={indices}
            activeSymbol={activeSymbol}
            onSelect={setActiveSymbol}
          />
        </div>
      </div>
      {/* 右侧图表区 */}
      <div className="w-full flex flex-col px-0 md:px-4">
        <RangeSelector
          selected={selectedRange}
          loading={isQuoteLoading}
          onChange={setSelectedRange}
        />
        <div className="border rounded-2xl bg-white dark:bg-gray-900 p-4 min-h-36 flex items-center justify-center shadow-lg w-full transition-shadow duration-200">
          {isQuoteLoading ? (
            <div className="w-full flex flex-col items-center gap-2 animate-pulse">
              <div className="w-4/5 h-8 bg-gray-200 dark:bg-gray-800 rounded mb-2"></div>
              <div className="w-full h-24 bg-gray-100 dark:bg-gray-800 rounded"></div>
            </div>
          ) : (
            <>
              <StatusHint
                loading={isQuoteLoading}
                error={isQuoteError}
                empty={quoteList.length === 0}
                loadingText={t("loading_data")}
                errorText={t("error")}
                emptyText={t("no_data")}
              />
              {!isQuoteError && quoteList.length > 0 && (
                <LineChart data={quoteList} />
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}