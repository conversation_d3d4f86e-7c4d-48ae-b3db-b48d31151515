import i18n from "i18next";
import { initReactI18next } from "react-i18next";

const resources = {
  zh: {
    translation: {
      indices_title: "A股主要宽基指数",
      no_indices: "暂无指数数据",
      loading: "加载中...",
      loading_data: "数据加载中...",
      error: "数据加载失败，请稍后重试",
      no_data: "暂无数据",
      range_3m: "最近3个月",
      range_6m: "6个月",
      range_1y: "最近1年",
      name: "名称",
      code: "代码",
      latest_close: "最新收盘",
      change: "涨跌",
      change_percent: "涨跌幅",
      min: "最小值",
      max: "最大值",
    },
  },
  en: {
    translation: {
      indices_title: "Main Broad Indices (A-share)",
      no_indices: "No indices available",
      loading: "Loading...",
      loading_data: "Loading data...",
      error: "Failed to load data, please try again later",
      no_data: "No data",
      range_3m: "Last 3 Months",
      range_6m: "6 Months",
      range_1y: "Last 1 Year",
      name: "Name",
      code: "Code",
      latest_close: "Latest Close",
      change: "Change",
      change_percent: "Change %",
      min: "Min",
      max: "Max",
    },
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: "zh",
    fallbackLng: "zh",
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;