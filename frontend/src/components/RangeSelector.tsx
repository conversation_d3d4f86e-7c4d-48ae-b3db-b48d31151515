import React from "react";


type Range = "3m" | "6m" | "1y";

interface RangeSelectorProps {
  selected: Range;
  loading: boolean;
  onChange: (range: Range) => void;
}

/**
 * 区间选择组件，支持禁用与高亮
 */
const RangeSelector: React.FC<RangeSelectorProps> = ({ selected, loading, onChange }) => {
  const ranges: { label: string; value: Range }[] = [
    { label: "最近3个月", value: "3m" },
    { label: "6个月", value: "6m" },
    { label: "最近1年", value: "1y" },
  ];
  return (
    <div className="flex flex-wrap items-center mb-2 gap-2 w-full">
      {ranges.map(r => (
        <button
          key={r.value}
          className={`flex-1 min-w-[90px] px-4 py-1 rounded-lg border border-blue-600 font-medium transition-all duration-200 outline-none
            ${selected === r.value
              ? "bg-blue-600 text-white shadow-lg dark:bg-blue-700 dark:text-white scale-[1.04]"
              : "bg-white text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-300 dark:hover:bg-gray-700 hover:shadow-md hover:scale-[1.03]"}
            focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900
            ${loading ? "opacity-60 cursor-not-allowed" : ""}
          `}
          onClick={() => !loading && onChange(r.value)}
          disabled={loading}
          style={{ maxWidth: "160px" }}
          aria-pressed={selected === r.value}
          aria-label={r.label}
        >
          {r.label}
        </button>
      ))}
      {loading && (
        <span className="ml-2 text-blue-500 animate-pulse text-xs">数据加载中...</span>
      )}
    </div>
  );
};

export default RangeSelector;