import { useEffect, useRef, useState } from "react";
import { clientApiClient } from "../services/clientApi";
import type { QuoteItem } from "../types";

type Range = "3m" | "6m" | "1y";

interface UseQuoteDataResult {
  data: QuoteItem[];
  loading: boolean;
  error: boolean;
}

export function useQuoteData(symbol: string | null, range: Range): UseQuoteDataResult {
  const [data, setData] = useState<QuoteItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const abortRef = useRef<AbortController | null>(null);

  useEffect(() => {
    if (!symbol) {
      setData([]);
      setError(false);
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(false);

    // 取消上一次请求
    if (abortRef.current) {
      abortRef.current.abort();
    }
    const controller = new AbortController();
    abortRef.current = controller;

    let pageSize = 90;
    if (range === "6m") pageSize = 180;
    else if (range === "1y") pageSize = 365;

    clientApiClient.get(`/indices/${symbol}/quotes?page=1&page_size=${pageSize}`, {
      signal: controller.signal,
    })
      .then((res: { data?: any }) => {
        const arr = Array.isArray(res.data?.data) ? res.data.data : (Array.isArray(res.data) ? res.data : []);
        setData(arr);
        setLoading(false);
      })
      .catch((err: unknown) => {
        if (controller.signal.aborted) return;
        setData([]);
        setLoading(false);
        setError(true);
      });

    return () => {
      controller.abort();
    };
  }, [symbol, range]);

  return { data, loading, error };
}

export default useQuoteData;