"use client";
import React from "react";
import type { IndexItem } from "../types";


interface IndicesTableProps {
  indices: IndexItem[];
  activeSymbol: string | null;
  onSelect: (symbol: string) => void;
}

/**
 * 指数表格组件，支持高亮与兜底
 */
const IndicesTable: React.FC<IndicesTableProps> = ({ indices, activeSymbol, onSelect }) => {
  if (!Array.isArray(indices) || indices.length === 0) {
    return <div className="text-gray-400 text-center py-8">暂无指数数据</div>;
  }
  return (
    <div className="w-full overflow-x-auto">
      <table className="min-w-[480px] w-full border rounded-lg text-sm dark:bg-gray-900" role="table" aria-label="A股主要宽基指数">
        <thead className="bg-gray-100 dark:bg-gray-800 sticky top-0 z-10" role="rowgroup">
          <tr role="row">
            <th className="py-2 px-2 whitespace-nowrap text-gray-700 dark:text-gray-200" role="columnheader">名称</th>
            <th className="py-2 px-2 whitespace-nowrap text-gray-700 dark:text-gray-200" role="columnheader">代码</th>
            <th className="py-2 px-2 whitespace-nowrap text-gray-700 dark:text-gray-200" role="columnheader">最新收盘</th>
            <th className="py-2 px-2 whitespace-nowrap text-gray-700 dark:text-gray-200" role="columnheader">涨跌</th>
            <th className="py-2 px-2 whitespace-nowrap text-gray-700 dark:text-gray-200" role="columnheader">涨跌幅</th>
          </tr>
        </thead>
        <tbody role="rowgroup">
          {indices.map((idx) => {
            const isActive = activeSymbol === idx.symbol;
            return (
              <tr
                key={idx.symbol}
                role="row"
                aria-selected={isActive}
                className={
                  `border-t cursor-pointer transition-all duration-150
                  ${isActive
                    ? "bg-blue-50 dark:bg-blue-900 border-l-4 border-blue-600 shadow-lg font-semibold scale-[1.02] transition-all duration-200"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700 hover:scale-[1.01] transition-all duration-150"}`
                }
                onClick={() => onSelect(idx.symbol)}
              >
                <td className="py-2 px-2 whitespace-nowrap text-gray-900 dark:text-gray-100">{idx.name}</td>
                <td className="py-2 px-2 whitespace-nowrap text-gray-900 dark:text-gray-100">{idx.symbol}</td>
                <td className="py-2 px-2 whitespace-nowrap text-gray-900 dark:text-gray-100">{idx.latest_close}</td>
                <td className={`py-2 px-2 whitespace-nowrap ${idx.latest_change >= 0 ? "text-red-600 dark:text-red-400" : "text-green-600 dark:text-green-400"}`}>
                  {idx.latest_change}
                </td>
                <td className={`py-2 px-2 whitespace-nowrap ${idx.latest_change_percent >= 0 ? "text-red-600 dark:text-red-400" : "text-green-600 dark:text-green-400"}`}>
                  {idx.latest_change_percent}%
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default IndicesTable;
