"use client";
import React from "react";
import type { IndexItem } from "../types";


interface IndicesTableProps {
  indices: IndexItem[];
  activeSymbol: string | null;
  onSelect: (symbol: string) => void;
}

/**
 * 指数表格组件，支持高亮与兜底
 */
const IndicesTable: React.FC<IndicesTableProps> = ({ indices, activeSymbol, onSelect }) => {
  if (!Array.isArray(indices) || indices.length === 0) {
    return <div className="text-gray-400 text-center py-8">暂无指数数据</div>;
  }
  return (
    <div className="space-y-2">
      {indices.map((idx) => {
        const isActive = activeSymbol === idx.symbol;
        const isPositive = idx.latest_change >= 0;

        return (
          <div
            key={idx.symbol}
            role="button"
            tabIndex={0}
            aria-selected={isActive}
            className={`
              relative p-4 rounded-lg border cursor-pointer transition-all duration-200
              ${isActive
                ? "bg-blue-50 dark:bg-blue-900/50 border-blue-200 dark:border-blue-700 shadow-md ring-2 ring-blue-500/20"
                : "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
              }
            `}
            onClick={() => onSelect(idx.symbol)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                onSelect(idx.symbol);
              }
            }}
          >
            {/* 选中指示器 */}
            {isActive && (
              <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-l-lg"></div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h3 className={`font-medium truncate ${isActive ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-gray-100'}`}>
                    {idx.name}
                  </h3>
                  <span className={`text-xs px-2 py-1 rounded-full font-mono ${isActive ? 'bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200' : 'bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300'}`}>
                    {idx.symbol}
                  </span>
                </div>
                <div className="mt-1 flex items-center gap-4 text-sm">
                  <span className="text-gray-900 dark:text-gray-100 font-medium">
                    {idx.latest_close}
                  </span>
                  <div className="flex items-center gap-2">
                    <span className={`font-medium ${isPositive ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                      {isPositive ? '+' : ''}{idx.latest_change}
                    </span>
                    <span className={`text-xs px-1.5 py-0.5 rounded font-medium ${
                      isPositive
                        ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400'
                        : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400'
                    }`}>
                      {isPositive ? '+' : ''}{idx.latest_change_percent}%
                    </span>
                  </div>
                </div>
              </div>

              {/* 箭头指示器 */}
              <div className={`ml-2 transition-transform duration-200 ${isActive ? 'text-blue-500' : 'text-gray-400'}`}>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default IndicesTable;
