"use client";
import React from "react";
import type { IndexItem } from "../types";
import { useTranslation } from "react-i18next";

interface IndicesTableProps {
  indices: IndexItem[];
  activeSymbol: string | null;
  onSelect: (symbol: string) => void;
}

/**
 * 指数表格组件，支持高亮与兜底
 */
const IndicesTable: React.FC<IndicesTableProps> = ({ indices, activeSymbol, onSelect }) => {
  const { t } = useTranslation();
  if (!Array.isArray(indices) || indices.length === 0) {
    return <div className="text-gray-400 text-center py-8">{t("no_indices")}</div>;
  }
  return (
    <div className="w-full overflow-x-auto">
      <table className="min-w-[480px] w-full border rounded-lg text-sm dark:bg-gray-900" role="table" aria-label={t("indices_title")}>
        <thead className="bg-gray-100 dark:bg-gray-800 sticky top-0 z-10" role="rowgroup">
          <tr role="row">
            <th className="py-2 px-2 whitespace-nowrap text-gray-700 dark:text-gray-200" role="columnheader">{t("name")}</th>
            <th className="py-2 px-2 whitespace-nowrap text-gray-700 dark:text-gray-200" role="columnheader">{t("code")}</th>
            <th className="py-2 px-2 whitespace-nowrap text-gray-700 dark:text-gray-200" role="columnheader">{t("latest_close")}</th>
            <th className="py-2 px-2 whitespace-nowrap text-gray-700 dark:text-gray-200" role="columnheader">{t("change")}</th>
            <th className="py-2 px-2 whitespace-nowrap text-gray-700 dark:text-gray-200" role="columnheader">{t("change_percent")}</th>
          </tr>
        </thead>
        <tbody role="rowgroup">
          {indices.map((idx) => {
            const isActive = activeSymbol === idx.symbol;
            return (
              <tr
                key={idx.symbol}
                role="row"
                aria-selected={isActive}
                className={
                  `border-t cursor-pointer transition-all duration-150
                  ${isActive
                    ? "bg-blue-50 dark:bg-blue-900 border-l-4 border-blue-600 shadow-lg font-semibold scale-[1.02] transition-all duration-200"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700 hover:scale-[1.01] transition-all duration-150"}`
                }
                onClick={() => onSelect(idx.symbol)}
              >
                <td className="py-2 px-2 whitespace-nowrap text-gray-900 dark:text-gray-100">{idx.name}</td>
                <td className="py-2 px-2 whitespace-nowrap text-gray-900 dark:text-gray-100">{idx.symbol}</td>
                <td className="py-2 px-2 whitespace-nowrap text-gray-900 dark:text-gray-100">{idx.latest_close}</td>
                <td className={`py-2 px-2 whitespace-nowrap ${idx.latest_change >= 0 ? "text-red-600 dark:text-red-400" : "text-green-600 dark:text-green-400"}`}>
                  {idx.latest_change}
                </td>
                <td className={`py-2 px-2 whitespace-nowrap ${idx.latest_change_percent >= 0 ? "text-red-600 dark:text-red-400" : "text-green-600 dark:text-green-400"}`}>
                  {idx.latest_change_percent}%
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default IndicesTable;
