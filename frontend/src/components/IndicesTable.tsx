"use client";
import React from "react";
import type { IndexItem } from "../types";


interface IndicesTableProps {
  indices: IndexItem[];
  activeSymbol: string | null;
  onSelect: (symbol: string) => void;
}

/**
 * 指数表格组件，支持高亮与兜底
 */
const IndicesTable: React.FC<IndicesTableProps> = ({ indices, activeSymbol, onSelect }) => {
  if (!Array.isArray(indices) || indices.length === 0) {
    return <div className="text-gray-400 text-center py-8">暂无指数数据</div>;
  }
  return (
    <div className="overflow-hidden">
      <table className="w-full text-sm">
        <thead className="bg-gray-50 dark:bg-gray-700 sticky top-0">
          <tr>
            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              指数
            </th>
            <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              价格
            </th>
            <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              涨跌幅
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
          {indices.map((idx) => {
            const isActive = activeSymbol === idx.symbol;
            const isPositive = idx.latest_change >= 0;

            return (
              <tr
                key={idx.symbol}
                className={`
                  cursor-pointer transition-all duration-150
                  ${isActive
                    ? "bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-500"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700/50"
                  }
                `}
                onClick={() => onSelect(idx.symbol)}
              >
                <td className="px-3 py-3">
                  <div className="flex flex-col">
                    <div className={`font-medium truncate ${isActive ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-gray-100'}`}>
                      {idx.name}
                    </div>
                    <div className={`text-xs font-mono ${isActive ? 'text-blue-600 dark:text-blue-300' : 'text-gray-500 dark:text-gray-400'}`}>
                      {idx.symbol}
                    </div>
                  </div>
                </td>
                <td className="px-3 py-3 text-right">
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {idx.latest_close}
                  </div>
                  <div className={`text-xs ${isPositive ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                    {isPositive ? '+' : ''}{idx.latest_change}
                  </div>
                </td>
                <td className="px-3 py-3 text-right">
                  <span className={`
                    inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                    ${isPositive
                      ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                      : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                    }
                  `}>
                    {isPositive ? '+' : ''}{idx.latest_change_percent}%
                  </span>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default IndicesTable;
