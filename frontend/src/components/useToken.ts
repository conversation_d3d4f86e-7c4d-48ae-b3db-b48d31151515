import { useEffect, useState } from "react";
import { getSession } from "next-auth/react";

/**
 * 获取 token 的自定义 hook，兼容开发/生产环境
 */
export function useToken(): string {
  const [token, setToken] = useState("");

  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      setToken("");
      return;
    }
    getSession().then((session) => {
      const t =
        (session as any)?.accessToken ||
        (session as any)?.idToken ||
        (session as any)?.token ||
        "";
      setToken(t);
    });
  }, []);

  return token;
}

export default useToken;