import React from "react";

interface StatusHintProps {
  loading?: boolean;
  error?: boolean;
  empty?: boolean;
  loadingText?: string;
  errorText?: string;
  emptyText?: string;
}

/**
 * 统一加载/错误/空数据提示组件
 */
const StatusHint: React.FC<StatusHintProps> = ({
  loading,
  error,
  empty,
  loadingText,
  errorText,
  emptyText,
}) => {

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center h-24 text-blue-500 dark:text-blue-400 animate-pulse gap-2">
        <svg className="w-6 h-6 animate-spin text-blue-400" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
        </svg>
        {loadingText ?? "加载中..."}
      </div>
    );
  }
  if (error) {
    return (
      <div className="flex flex-col items-center w-full text-red-500 dark:text-red-400 font-medium mb-2 gap-2">
        <svg className="w-6 h-6 animate-bounce" fill="none" viewBox="0 0 24 24">
          <path d="M12 9v2m0 4h.01M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        {errorText ?? "数据加载失败，请稍后重试"}
        <button
          className="mt-2 px-3 py-1 rounded bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300 text-xs font-semibold hover:bg-red-200 dark:hover:bg-red-800 transition"
          onClick={() => window.location.reload()}
        >
          重试
        </button>
      </div>
    );
  }
  if (empty) {
    return (
      <div className="flex flex-col items-center w-full text-gray-400 dark:text-gray-500 mb-2 gap-2">
        <svg className="w-8 h-8 mb-1" fill="none" viewBox="0 0 24 24">
          <path d="M12 4v16m8-8H4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        {emptyText ?? "暂无数据"}
        <span className="text-xs text-gray-300 dark:text-gray-600">可尝试切换区间或选择其他指数</span>
      </div>
    );
  }
  return null;
};

export default StatusHint;