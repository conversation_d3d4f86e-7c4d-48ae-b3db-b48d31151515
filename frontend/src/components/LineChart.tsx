import React from "react";
import type { QuoteItem } from "../types";


/**
 * 折线图组件，响应式设计，数据校验与兜底，性能优化（memo）
 */
const LineChart: React.FC<{ data: QuoteItem[] }> = React.memo(({ data }) => {
  if (!Array.isArray(data) || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
        暂无数据
      </div>
    );
  }

  const closes = data.map(q => q.close);
  const min = Math.min(...closes);
  const max = Math.max(...closes);
  const range = max - min || 1;

  // 响应式SVG尺寸
  const viewBoxWidth = 800;
  const viewBoxHeight = 400;
  const padding = 60;

  // 计算点坐标
  const points = closes.map((close, i) => {
    const x = padding + ((viewBoxWidth - 2 * padding) * i) / (closes.length - 1);
    const y = viewBoxHeight - padding - ((viewBoxHeight - 2 * padding) * (close - min)) / range;
    return `${x},${y}`;
  }).join(" ");

  // 创建网格线
  const gridLines = [];
  const numGridLines = 5;
  for (let i = 0; i <= numGridLines; i++) {
    const y = padding + ((viewBoxHeight - 2 * padding) * i) / numGridLines;
    gridLines.push(
      <line
        key={`grid-${i}`}
        x1={padding}
        y1={y}
        x2={viewBoxWidth - padding}
        y2={y}
        stroke="currentColor"
        strokeWidth="0.5"
        className="text-gray-300 dark:text-gray-600"
        opacity="0.5"
      />
    );
  }

  // 计算显示的价格标签
  const priceLabels = [];
  for (let i = 0; i <= numGridLines; i++) {
    const value = max - (range * i) / numGridLines;
    const y = padding + ((viewBoxHeight - 2 * padding) * i) / numGridLines;
    priceLabels.push(
      <text
        key={`label-${i}`}
        x={padding - 10}
        y={y + 4}
        fontSize="12"
        textAnchor="end"
        className="fill-gray-600 dark:fill-gray-400"
      >
        {value.toFixed(2)}
      </text>
    );
  }

  return (
    <div className="w-full h-full flex flex-col">
      {/* 图表标题和统计信息 */}
      <div className="flex items-center justify-between mb-4 px-2">
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span className="text-gray-600 dark:text-gray-400">收盘价</span>
          </div>
          <div className="text-gray-500 dark:text-gray-400">
            共 {data.length} 个数据点
          </div>
        </div>
        <div className="flex items-center gap-4 text-sm">
          <div className="text-green-600 dark:text-green-400">
            最低: {min.toFixed(2)}
          </div>
          <div className="text-red-600 dark:text-red-400">
            最高: {max.toFixed(2)}
          </div>
        </div>
      </div>

      {/* SVG 图表 */}
      <div className="flex-1 min-h-0">
        <svg
          viewBox={`0 0 ${viewBoxWidth} ${viewBoxHeight}`}
          className="w-full h-full"
          role="img"
          aria-label="指数走势图"
        >
          {/* 背景 */}
          <rect
            width={viewBoxWidth}
            height={viewBoxHeight}
            fill="transparent"
          />

          {/* 网格线 */}
          {gridLines}

          {/* 价格标签 */}
          {priceLabels}

          {/* 主要折线 */}
          <polyline
            fill="none"
            stroke="#3b82f6"
            strokeWidth="2"
            points={points}
            className="drop-shadow-sm"
          />

          {/* 数据点 */}
          {closes.map((close, i) => {
            const x = padding + ((viewBoxWidth - 2 * padding) * i) / (closes.length - 1);
            const y = viewBoxHeight - padding - ((viewBoxHeight - 2 * padding) * (close - min)) / range;
            return (
              <circle
                key={i}
                cx={x}
                cy={y}
                r="2"
                fill="#3b82f6"
                className="opacity-60 hover:opacity-100 hover:r-3 transition-all duration-200"
              >
                <title>{`第${i + 1}个数据点: ${close.toFixed(2)}`}</title>
              </circle>
            );
          })}

          {/* 渐变填充区域 */}
          <defs>
            <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.2" />
              <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.05" />
            </linearGradient>
          </defs>
          <polygon
            fill="url(#areaGradient)"
            points={`${padding},${viewBoxHeight - padding} ${points} ${viewBoxWidth - padding},${viewBoxHeight - padding}`}
          />
        </svg>
      </div>
    </div>
  );
});
LineChart.displayName = "LineChart";

export default LineChart;