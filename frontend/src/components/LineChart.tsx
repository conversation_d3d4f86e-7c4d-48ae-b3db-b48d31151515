import React from "react";
import type { QuoteItem } from "../types";
import { useTranslation } from "react-i18next";

/**
 * 折线图组件，数据校验与兜底，性能优化（memo）
 */
const LineChart: React.FC<{ data: QuoteItem[] }> = React.memo(({ data }) => {
  const { t } = useTranslation();
  if (!Array.isArray(data) || data.length === 0) {
    return <div>{t("no_data")}</div>;
  }
  const w = 320, h = 120, pad = 30;
  const closes = data.map(q => q.close);
  const min = Math.min(...closes), max = Math.max(...closes);
  const points = closes.map((c, i) => {
    const x = pad + ((w - 2 * pad) * i) / (closes.length - 1);
    const y = h - pad - ((h - 2 * pad) * (c - min)) / (max - min || 1);
    return `${x},${y}`;
  }).join(" ");
  return (
    <svg
      width={w}
      height={h}
      className="my-4 bg-white dark:bg-gray-900 rounded border"
      role="img"
      aria-label={t("indices_title") + " " + t("折线图")}
    >
      <polyline
        fill="none"
        stroke="#2563eb"
        strokeWidth="2"
        points={points}
      />
      <text x={2} y={h - pad} fontSize="10" className="fill-gray-700 dark:fill-gray-200">{t("min")}: {min}</text>
      <text x={2} y={pad} fontSize="10" className="fill-gray-700 dark:fill-gray-200">{t("max")}: {max}</text>
    </svg>
  );
});
LineChart.displayName = "LineChart";

export default LineChart;