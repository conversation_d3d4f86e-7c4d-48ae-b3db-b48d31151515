import axios from 'axios';
import type { IndexItem } from "../types";

// API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6008/api';

// 创建axios实例
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 注意：移除了自动注入 NextAuth token 的拦截器
// 因为在服务器组件中调用 getSession() 会导致 "React Context is unavailable in Server Components" 错误
// 现在所有需要认证的API调用都需要显式传递token

// 指数行情相关API
export const getIndices = async () => {
  try {
    const response = await apiClient.get('/indices');
    return response.data;
  } catch (error) {
    throw new Error('无法获取指数行情数据');
  }
};

export const getIndexDetail = async (symbol: string) => {
  try {
    const response = await apiClient.get(`/indices/${symbol}`);
    return response.data;
  } catch (error) {
    throw new Error('无法获取指数详情');
  }
};
// 指数报价（quotes）API
export const getIndexQuotes = async (symbol: string, token: string) => {
  try {
    const response = await apiClient.get(`/indices/${symbol}/quotes?page=1&page_size=20`, {
      headers: token ? { Authorization: `Bearer ${token}` } : undefined
    });
    if (response.status < 200 || response.status >= 300) {
      return { items: [], total: 0 };
    }
    return response.data;
  } catch (error) {
    return { items: [], total: 0 };
  }
};

// 指数最新（latest）API
export const getIndexLatest = async (symbol: string, token: string) => {
  try {
    const response = await apiClient.get(`/indices/${symbol}/latest`, {
      headers: token ? { Authorization: `Bearer ${token}` } : undefined
    });
    if (response.status < 200 || response.status >= 300) {
      return null;
    }
    return response.data;
  } catch (error) {
    return null;
  }
}
// 获取指数列表，错误时返回空数组
export async function getIndicesList(token: string): Promise<IndexItem[]> {
  try {
    const response = await apiClient.get('/indices', {
      headers: { Authorization: `Bearer ${token}` }
    });
    if (response.status < 200 || response.status >= 300) {
      return [];
    }
    // 保证始终返回数组类型
    const data = response?.data?.data;
    if (Array.isArray(data)) {
      return data;
    }
    return [];
  } catch (error) {
    return [];
  }
}