import axios from 'axios';
import type { IndexItem } from "../types";

// API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6008/api';

// 创建axios实例
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 自动注入 NextAuth token
import { getSession } from "next-auth/react";
// 优化 token 注入逻辑：生产环境自动获取 session 注入 token，开发环境仅用页面传递的 token
apiClient.interceptors.request.use(
  async (config) => {
    if (process.env.NODE_ENV !== 'development') {
      // 生产环境：自动获取 session 并注入 token
      const session = await getSession();
      const token =
        (session as any)?.accessToken ||
        (session as any)?.idToken ||
        (session as any)?.token;
      if (token) {
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    // 开发环境：不自动获取 session，不做任何 token 注入，直接用页面传递的 token（如业务 API 通过 headers 传递）
    return config;
  },
  (error) => Promise.reject(error)
);

// 指数行情相关API
export const getIndices = async () => {
  try {
    const response = await apiClient.get('/indices');
    return response.data;
  } catch (error) {
    throw new Error('无法获取指数行情数据');
  }
};

export const getIndexDetail = async (symbol: string) => {
  try {
    const response = await apiClient.get(`/indices/${symbol}`);
    return response.data;
  } catch (error) {
    throw new Error('无法获取指数详情');
  }
};
// 指数报价（quotes）API
export const getIndexQuotes = async (symbol: string, token: string) => {
  try {
    const response = await apiClient.get(`/indices/${symbol}/quotes?page=1&page_size=20`, {
      // token参数暂未使用，保留接口一致性
    });
    if (response.status < 200 || response.status >= 300) {
      return { items: [], total: 0 };
    }
    return response.data;
  } catch (error) {
    return { items: [], total: 0 };
  }
};

// 指数最新（latest）API
export const getIndexLatest = async (symbol: string, token: string) => {
  try {
    const response = await apiClient.get(`/indices/${symbol}/latest`, {
      // token参数暂未使用，保留接口一致性
    });
    if (response.status < 200 || response.status >= 300) {
      return null;
    }
    return response.data;
  } catch (error) {
    return null;
  }
  
}
// 获取指数列表，错误时返回空数组
export async function getIndicesList(token: string): Promise<IndexItem[]> {
  try {
    const response = await apiClient.get('/indices', {
      headers: { Authorization: `Bearer ${token}` }
    });
    if (response.status < 200 || response.status >= 300) {
      return [];
    }
    // 保证始终返回数组类型
    const data = response?.data?.data;
    if (Array.isArray(data)) {
      return data;
    }
    return [];
  } catch (error) {
    return [];
  }
}