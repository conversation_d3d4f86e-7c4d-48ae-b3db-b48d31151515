'use client';

import axios from 'axios';
import { getSession } from "next-auth/react";

// API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6008/api';

// 创建客户端专用的axios实例
export const clientApiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 客户端自动注入 NextAuth token 的拦截器
// 只在客户端组件中使用，避免服务器组件中的Context错误
clientApiClient.interceptors.request.use(
  async (config) => {
    if (process.env.NODE_ENV !== 'development') {
      // 生产环境：自动获取 session 并注入 token
      const session = await getSession();
      const token =
        (session as any)?.accessToken ||
        (session as any)?.idToken ||
        (session as any)?.token;
      if (token) {
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    // 开发环境：不自动获取 session，不做任何 token 注入
    return config;
  },
  (error) => Promise.reject(error)
);
